<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Gider bilgileri yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <div class="row">
    <!-- Summary Cards Row (Günlük, Aylık, Yıllık) -->
    <div class="col-md-12 mb-4">
      <div class="row">
        <!-- Daily Total Expense Card -->
        <div class="col-md-4 mb-3">
          <div class="modern-stats-card daily-expense-card">
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-day"></i>
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalDailyExpense | number:'1.2-2':'tr' }} ₺</h3>
              <p class="modern-stats-label">Bugün Toplam Gider</p>
            </div>
          </div>
        </div>
        <!-- Monthly Total Expense Card -->
        <div class="col-md-4 mb-3">
          <div class="modern-stats-card monthly-expense-card">
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalMonthlyExpense | number:'1.2-2':'tr' }} ₺</h3>
              <p class="modern-stats-label">Bu Ay Toplam Gider</p>
            </div>
          </div>
        </div>
         <!-- Yearly Total Expense Card -->
         <div class="col-md-4 mb-3">
          <div class="modern-stats-card yearly-expense-card">
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-week"></i>
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalYearlyExpense | number:'1.2-2':'tr' }} ₺</h3>
              <p class="modern-stats-label">Bu Yıl Toplam Gider</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="col-md-12 mb-4">
      <div class="row">
        <!-- Expense Distribution Chart -->
        <div class="col-md-6 mb-4">
          <div class="modern-card h-100">
            <div class="modern-card-header">
              <h5 class="mb-0">{{ getDistributionChartTitle() }}</h5>
            </div>
            <div class="modern-card-body">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="expenseDistributionChart" *ngIf="!isDistributionChartEmpty()"></canvas>
                <div *ngIf="isDistributionChartEmpty()" class="empty-chart-message">
                  <div class="text-center text-muted">
                    <i class="fas fa-chart-pie fa-3x mb-3"></i>
                    <p class="mb-0">Bu dönemde gider bulunmuyor</p>
                    <small>Gider eklemek için yukarıdaki "Gider Ekle" butonunu kullanabilirsiniz</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="col-md-6 mb-4">
          <div class="modern-card h-100">
            <div class="modern-card-header">
              <h5 class="mb-0">Aylık Gider Trendi ({{ getCurrentYear() }})</h5>
            </div>
            <div class="modern-card-body">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="expenseMonthlyTrendChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content: Table and Filters -->
    <div class="col-md-12">
      <div class="modern-card">
        <!-- Card Header: Title, Actions -->
        <div class="modern-card-header">
          <h5 class="mb-0">Gider Kayıtları</h5>
          <div class="d-flex gap-2 align-items-center flex-wrap">
            <!-- Export Buttons -->
            <button class="modern-btn modern-btn-outline-primary modern-btn-sm"
                    (click)="exportToExcel()"
                    [disabled]="isExporting"
                    title="Excel'e Aktar">
              <i class="fas fa-spinner fa-spin me-1" *ngIf="isExporting"></i>
              <fa-icon [icon]="faFileExcel" class="modern-btn-icon" *ngIf="!isExporting"></fa-icon>
              {{ isExporting ? 'Hazırlanıyor...' : 'Excel' }}
            </button>
            <!-- Add Expense Button -->
            <button class="modern-btn modern-btn-primary modern-btn-sm" (click)="openExpenseDialog()">
              <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon> Yeni Gider Ekle
            </button>
          </div>
        </div>

        <!-- Advanced Filters -->
        <div class="modern-card-body border-bottom">
          <div class="row g-3">
            <!-- Search Filter -->
            <div class="col-md-2">
              <div class="search-input-container">
                <i class="fas fa-search search-icon"></i>
                <input
                  type="text"
                  class="search-input modern-form-control modern-form-control-sm"
                  placeholder="Tür veya açıklama ara..."
                  [formControl]="searchControl"
                  (input)="onFilterInputChange()" />
              </div>
            </div>

            <!-- Date Range Filter -->
            <div class="col-md-2">
              <input
                type="date"
                class="modern-form-control modern-form-control-sm"
                placeholder="Başlangıç Tarihi"
                [(ngModel)]="filterState.startDate"
                (ngModelChange)="onFilterInputChange()" />
            </div>
            <div class="col-md-2">
              <input
                type="date"
                class="modern-form-control modern-form-control-sm"
                placeholder="Bitiş Tarihi"
                [(ngModel)]="filterState.endDate"
                (ngModelChange)="onFilterInputChange()" />
            </div>

            <!-- Expense Type Filter -->
            <div class="col-md-2">
              <select
                class="modern-form-control modern-form-control-sm"
                [(ngModel)]="filterState.expenseType"
                (ngModelChange)="onFilterInputChange()">
                <option value="">Tüm Türler</option>
                <option *ngFor="let type of expenseTypes" [value]="type">{{ type }}</option>
              </select>
            </div>

            <!-- Amount Range Filter -->
            <div class="col-md-1">
              <input
                type="number"
                class="modern-form-control modern-form-control-sm"
                placeholder="Min ₺"
                [(ngModel)]="filterState.minAmount"
                (ngModelChange)="onFilterInputChange()" />
            </div>
            <div class="col-md-1">
              <input
                type="number"
                class="modern-form-control modern-form-control-sm"
                placeholder="Max ₺"
                [(ngModel)]="filterState.maxAmount"
                (ngModelChange)="onFilterInputChange()" />
            </div>

            <!-- Filter Actions -->
            <div class="col-md-2">
              <div class="d-flex gap-2 w-100">
                <button
                  *ngIf="shouldShowSearchButton()"
                  class="btn btn-primary btn-sm flex-fill"
                  (click)="performSearch()"
                  [disabled]="isSearching"
                  title="Filtreleri Uygula">
                  <span *ngIf="!isSearching">
                    <i class="fas fa-search me-1"></i>
                    Ara
                  </span>
                  <span *ngIf="isSearching">
                    <i class="fas fa-spinner fa-spin me-1"></i>
                    Aranıyor...
                  </span>
                </button>
                <button
                  class="btn btn-secondary btn-sm flex-fill"
                  (click)="clearFilters()"
                  [disabled]="!hasActiveFilters() || isSearching"
                  title="Filtreleri Temizle">
                  <i class="fas fa-times me-1"></i>
                  Temizle
                </button>
              </div>
            </div>
          </div>

          <!-- Favori filtreler kaldırıldı -->

          <!-- Favori filtreler kaldırıldı -->

          <!-- Dashboard filters kaldırıldı -->
        </div>

        <!-- Card Body: Table and Pagination -->
        <div class="modern-card-body">
          <!-- Table Info and Page Size -->
          <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="table-info">
              <span class="text-muted">
                Toplam {{ paginatedExpenses.totalCount }} kayıttan
                {{ (paginatedExpenses.pageNumber - 1) * paginatedExpenses.pageSize + 1 }}-{{
                  Math.min(paginatedExpenses.pageNumber * paginatedExpenses.pageSize, paginatedExpenses.totalCount)
                }} arası gösteriliyor
              </span>
            </div>
            <div class="page-size-selector">
              <label class="form-label me-2 mb-0">Sayfa başına:</label>
              <select class="modern-form-control modern-form-control-sm"
                      [(ngModel)]="paginatedExpenses.pageSize"
                      (ngModelChange)="changePageSize($event)"
                      style="width: auto; display: inline-block;">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
              </select>
            </div>
          </div>

          <!-- Expense Table -->
          <div class="table-responsive" *ngIf="!isLoading && paginatedExpenses.data.length > 0">
            <table class="modern-table">
              <thead>
                <tr>
                  <th class="sortable" (click)="sortBy('ExpenseType')">
                    Gider Türü
                    <fa-icon [icon]="getSortIcon('ExpenseType')" class="ms-1"></fa-icon>
                  </th>
                  <th style="text-align: right;" class="sortable" (click)="sortBy('Amount')">
                    Tutar
                    <fa-icon [icon]="getSortIcon('Amount')" class="ms-1"></fa-icon>
                  </th>
                  <th class="sortable" (click)="sortBy('ExpenseDate')">
                    Tarih
                    <fa-icon [icon]="getSortIcon('ExpenseDate')" class="ms-1"></fa-icon>
                  </th>
                  <th class="sortable" (click)="sortBy('Description')">
                    Açıklama
                    <fa-icon [icon]="getSortIcon('Description')" class="ms-1"></fa-icon>
                  </th>
                  <th style="text-align: center;">İşlem</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let expense of paginatedExpenses.data; let i = index" [style.animation-delay]="i * 0.05 + 's'">
                  <td>
                    <div style="display: flex; align-items: center;">
                      <div class="modern-avatar" [style.background-color]="getExpenseTypeColor(expense.expenseType)" style="margin-right: 0.75rem;">
                        <i [class]="getExpenseTypeIcon(expense.expenseType)"></i>
                      </div>
                      <div>
                        <div style="font-weight: 500;">{{ expense.expenseType || 'Belirtilmemiş' }}</div>
                        <small style="color: var(--text-secondary);">{{ getExpenseTypeTooltip(expense.expenseType) }}</small>
                      </div>
                    </div>
                  </td>
                  <td style="text-align: right; font-weight: 700;">
                    {{ expense.amount | currency:'₺':'symbol':'1.2-2':'tr' }}
                  </td>
                  <td>{{ expense.expenseDate | date:'dd.MM.yyyy HH:mm' }}</td>
                  <td>
                    <div *ngIf="expense.description; else noDescription" [title]="expense.description">
                      {{
                        expense.description.length > 50 ?
                        (expense.description | slice:0:50) + '...' :
                        expense.description
                      }}
                    </div>
                    <ng-template #noDescription>
                      <span style="color: var(--text-secondary); font-style: italic;">Açıklama yok</span>
                    </ng-template>
                  </td>
                  <td style="text-align: center;">
                    <div style="display: flex; justify-content: center; gap: 0.5rem;">
                      <button
                        class="modern-btn modern-btn-sm modern-btn-outline-primary"
                        style="background-color: var(--primary-light); color: var(--primary); border: none;"
                        (click)="openExpenseDialog(expense)"
                        title="Gideri Düzenle"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                      <button
                        class="modern-btn modern-btn-sm modern-btn-outline-primary"
                        style="background-color: var(--danger-light); color: var(--danger); border: none;"
                        (click)="deleteExpense(expense)"
                        title="Gideri Sil"
                      >
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 1.5rem; flex-wrap: wrap; gap: 1rem;" *ngIf="paginatedExpenses.totalPages > 0">
            <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
              <div style="color: var(--text-secondary);">
                Toplam {{ paginatedExpenses.totalCount }} kayıttan {{ (paginatedExpenses.pageNumber - 1) * paginatedExpenses.pageSize + 1 }} -
                {{ paginatedExpenses.pageNumber * paginatedExpenses.pageSize > paginatedExpenses.totalCount ? paginatedExpenses.totalCount : paginatedExpenses.pageNumber * paginatedExpenses.pageSize }} arası gösteriliyor
              </div>
              <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span style="color: var(--text-secondary); font-size: 0.9rem;">Sayfa başına:</span>
                <select
                  class="form-select form-select-sm"
                  style="width: auto; min-width: 70px;"
                  [value]="paginatedExpenses.pageSize"
                  (change)="changePageSize(+$any($event.target).value)"
                >
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>
            <nav aria-label="Page navigation">
              <ul class="modern-pagination">
                <li class="modern-page-item" [class.disabled]="paginatedExpenses.pageNumber === 1">
                  <a class="modern-page-link" href="javascript:void(0)" (click)="goToPage(paginatedExpenses.pageNumber - 1)" style="border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);">
                    <i class="fas fa-chevron-left"></i>
                  </a>
                </li>
                <li
                  class="modern-page-item"
                  *ngFor="let page of getPageNumbers()"
                  [class.active]="page === paginatedExpenses.pageNumber"
                >
                  <a class="modern-page-link" href="javascript:void(0)" (click)="goToPage(page)">
                    {{ page }}
                  </a>
                </li>
                <li class="modern-page-item" [class.disabled]="paginatedExpenses.pageNumber === paginatedExpenses.totalPages">
                  <a class="modern-page-link" href="javascript:void(0)" (click)="goToPage(paginatedExpenses.pageNumber + 1)" style="border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;">
                    <i class="fas fa-chevron-right"></i>
                  </a>
                </li>
              </ul>
            </nav>
          </div>

          <!-- Empty State -->
          <div class="empty-state" *ngIf="!isLoading && paginatedExpenses.data.length === 0">
            <i class="fas fa-receipt fa-3x text-muted"></i>
            <h5 class="mt-3">Gider Bulunamadı</h5>
            <p class="text-muted">Seçilen kriterlere uygun gider kaydı bulunamadı.</p>
            <button class="btn btn-primary" (click)="resetFilters()">
              Filtreleri Temizle
            </button>
          </div>

        </div> <!-- End Card Body -->
      </div> <!-- End Modern Card -->
    </div> <!-- End Col -->
    </div> <!-- End Row -->

  </div> <!-- End Main Content -->
</div> <!-- End Container -->