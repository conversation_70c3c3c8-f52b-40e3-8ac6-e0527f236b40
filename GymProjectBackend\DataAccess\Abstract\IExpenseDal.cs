using Core.DataAccess;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IExpenseDal : IEntityRepository<Expense>
    {
        // Pagination ve gelişmiş filtreleme için yeni metotlar
        PaginatedResult<ExpenseDto> GetExpensesPaginated(ExpensePagingParameters parameters);
        List<ExpenseDto> GetAllExpensesFiltered(ExpensePagingParameters parameters); // Export için

        // Ta<PERSON>h aralığı ve dashboard metodları
        List<ExpenseDto> GetExpensesByDateRange(DateTime startDate, DateTime endDate);
        ExpenseDashboardDto GetExpenseDashboardData(int year, int month);
        object GetMonthlyExpenses(int year);

        // SOLID prensiplerine uygun: Complex business operations
        Core.Utilities.Results.IResult SoftDeleteExpense(int expenseId, int companyId);
        Core.Utilities.Results.IResult AddExpenseWithBusinessLogic(Expense expense, int companyId);
        Core.Utilities.Results.IResult UpdateExpenseWithBusinessLogic(Expense expense, int companyId);
    }
}